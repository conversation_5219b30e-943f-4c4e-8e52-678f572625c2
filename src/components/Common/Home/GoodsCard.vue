<template>
    <div class="goods-card">
        <!-- 商品图片区域 -->
        <div class="goods-image">
            <img :src="goodsInfo.image" :alt="goodsInfo.name" />
        </div>

        <!-- 商品信息区域 -->
        <div class="goods-info">
            <h3 class="goods-name">{{ goodsInfo.name }}</h3>
            <div class="goods-details">
                <span class="goods-price">￥{{ goodsInfo.price }}</span>
                <span class="goods-sales" v-if="goodsInfo.sales > 0">销量：{{ goodsInfo.sales }}</span>
            </div>
            <div class="goods-spec" v-if="goodsInfo.spec">
                {{ goodsInfo.spec }}
            </div>
        </div>
    </div>
</template>

<script setup>
// 定义组件props
const props = defineProps({
    goodsInfo: {
        type: Object,
        required: true,
        default: () => ({
            image: '',
            name: '',
            price: 0,
            sales: 0,
            spec: '',
        })
    }
})
</script>

<style scoped lang="less">
.goods-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    // border: 1px solid #f5f5f5;

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
}

.goods-image {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
    background: #f8f9fa;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    &:hover img {
        transform: scale(1.02);
    }
}

.goods-info {
    padding: 16px 14px 14px;
    background: linear-gradient(180deg, #fff 0%, #fafbfc 100%);

    .goods-name {
        font-size: 15px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 12px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        letter-spacing: -0.2px;
    }

    .goods-details {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 8px;
        gap: 8px;

        .goods-price {
            color: #ff3b30;
            font-size: 20px;
            font-weight: 700;
            line-height: 1;
            flex-shrink: 0;
        }

        .goods-sales {
            color: #8e8e93;
            font-size: 13px;
            line-height: 1;
            white-space: nowrap;
            background: #f2f2f7;
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: 500;
        }
    }

    .goods-spec {
        color: #8e8e93;
        font-size: 12px;
        line-height: 1.2;
        background: #f8f9fa;
        padding: 6px 10px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        margin-top: 4px;
        font-weight: 500;
    }
}
</style>
